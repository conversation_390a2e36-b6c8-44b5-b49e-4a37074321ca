import React, { useState } from 'react';
import { StyleSheet, View, Dimensions, ScrollView, Text } from 'react-native';
import analytics from '@react-native-firebase/analytics';
import remoteConfig from '@react-native-firebase/remote-config';
import { AdComponent } from '../Components/ads/AdComponent';
import AboutUs from '../Components/Messages/AboutUs';
import { AppCard } from '../Components/Messages/AppCard';

const { width: screenWidth } = Dimensions.get('window');

export default function AboutUsPage() {
  analytics().logEvent('ads_tab_viewed');

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        bounces={true}>
        <AdComponent
          nativeId={remoteConfig().getValue('BoredNative').asString()}
          bannerId={remoteConfig().getValue('AdBanner').asString()}
          showAdMedia={true}
        />

        <View style={styles.spacer} />
        <AboutUs />
        <View style={styles.spacer} />

        {/* Our Other Apps Section */}
        <View style={styles.appsSection}>
          <Text style={styles.appsSectionTitle}>
            🚀 Discover Our Other Apps
          </Text>
          <Text style={styles.appsSectionSubtitle}>
            Explore more amazing tools we've built for you
          </Text>

          <View style={styles.appsGrid}>
            <AppCard
              icon="🎨"
              title="SVG Viewer Pro"
              description="Professional SVG file viewer and editor"
              packageId="com.svgviewerapp"
            />
            <AppCard
              icon="🔋"
              title="Battery Charger"
              description="Monitor your battery charging stats"
              packageId="com.chargingstats"
            />
            <AppCard
              icon="💬"
              title="Quick Chat"
              description="WhatsApp without saving numbers"
              packageId="com.quickchat.plustech"
            />
            <AppCard
              icon="🏦"
              title="Bank Balance"
              description="Check bank balance via SMS"
              packageId="com.banksms"
            />
            <AppCard
              icon="📱"
              title="QR Generator"
              description="Create and scan QR codes instantly"
              packageId="com.pt.qrcodegenerator"
            />
            <AppCard
              icon="📧"
              title="Mail Templates Pro"
              description="Professional email templates"
              packageId="com.instantemailcomposer"
            />
          </View>
        </View>

        <View style={styles.spacer} />
        <AdComponent
          nativeId={remoteConfig().getValue('BoredNativeTwo').asString()}
          bannerId={remoteConfig().getValue('BoredBannerTwo').asString()}
          showAdMedia={true}
        />

        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  loadingContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666666',
  },
  errorContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    fontSize: 18,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666666',
  },
  adFreeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  adFreeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 10,
    textAlign: 'center',
  },
  adFreeSubtext: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  bannerAd: {
    width: screenWidth,
    alignSelf: 'center',
    marginVertical: 10,
  },
  secondaryBannerAd: {
    width: screenWidth,
    alignSelf: 'center',
  },
  spacer: {
    height: 20,
    backgroundColor: '#F5F5F5',
    width: '100%',
  },
  bottomPadding: {
    height: 50,
    width: '100%',
  },
  // Apps Section Styles
  appsSection: {
    width: '100%',
    paddingHorizontal: 20,
    paddingVertical: 25,
    backgroundColor: '#FFFFFF',
  },
  appsSectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
  },
  appsSectionSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 22,
  },
  appsGrid: {
    gap: 15,
  },
});
