{"name": "friendly-chat", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "start": "node index.js", "nodemon": "nodemon index.js", "ngrok": "ngrok http 3000", "upload": "upload.bat", "clear_jest": "jest --clear<PERSON>ache"}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"@google-cloud/storage": "^5.18.1", "body-parser": "^1.19.1", "dotenv": "^10.0.0", "express": "^4.17.2", "leo-profanity": "^1.5.0", "mongoose": "^6.1.6", "request": "^2.88.2", "socket.io": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-transform-runtime": "^7.16.8", "@babel/preset-env": "^7.16.8", "@babel/runtime": "^7.16.7", "babel-loader": "^8.2.3", "babel-plugin-transform-runtime": "^6.23.0", "eslint": "^8.6.0", "jest": "^27.4.7", "nodemon": "^2.0.15", "prettier": "^2.5.1", "socket.io-client": "^4.4.1", "supertest": "^6.1.6"}}