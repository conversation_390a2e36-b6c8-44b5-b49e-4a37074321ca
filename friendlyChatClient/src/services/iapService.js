import { Alert, Platform } from 'react-native';
import {
  initConnection,
  purchaseErrorListener,
  purchaseUpdatedListener,
  finishTransaction,
  requestPurchase,
  getSubscriptions,
  requestSubscription,
  getProducts,
  getAvailablePurchases,
  PurchaseError,
} from 'react-native-iap';
import { IAP_SKUS } from '../Constants/iapConstants';

class IAPService {
  constructor() {
    this.lastErrorTimestamp = 0;
    this.ERROR_DEBOUNCE_MS = 1000; // 1 second debounce
    this.purchaseUpdateSubscription = null;
    this.purchaseErrorSubscription = null;
    this.onPurchaseComplete = null;
  }

  async init() {
    try {
      console.log('Initializing IAP...');

      await initConnection();
      await this.isAdFree(); // Check if the user already owns the remove ads feature
      this.setupListeners();
    } catch (error) {
      const timestamp = new Date().toISOString();
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';
      console.error(`Error initializing IAP at ${timestamp}:`, error);
    }
  }

  getSubscriptionExpiryDate(purchase) {
    if (Platform.OS === 'ios') {
      // iOS: Use original transaction date + subscription period
      return purchase.originalTransactionDateIOS
        ? new Date(
          purchase.originalTransactionDateIOS + 30 * 24 * 60 * 60 * 1000,
        )
        : null;
    } else {
      // Android: Use subscription expiry date
      return purchase.subscriptionPeriodAndroid
        ? new Date(
          parseInt(purchase.purchaseTimeMillis) +
          parseInt(purchase.subscriptionPeriodAndroid),
        )
        : null;
    }
  }

  isSubscriptionActive(purchase) {
    if (!purchase) return false;

    const expiryDate = this.getSubscriptionExpiryDate(purchase);
    if (!expiryDate) return false;

    return expiryDate > new Date();
  }

  async isAdFree() {
    try {
      const purchases = await getAvailablePurchases();

      // Check subscription status
      const subscription = purchases.find(
        purchase => purchase.productId === IAP_SKUS.REMOVE_ADS_Subscription,
      );

      if (subscription) {
        const subscriptionExpiryDate =
          this.getSubscriptionExpiryDate(subscription);
        console.log(`Subscription expiry date: ${subscriptionExpiryDate}`);
        if (subscriptionExpiryDate && subscriptionExpiryDate > new Date()) {
          return true;
        }
      }

      // Check one-time purchase status
      const oneMonthPurchase = purchases.find(
        purchase => purchase.productId === IAP_SKUS.ONE_MONTH_AD_FREE,
      );

      if (oneMonthPurchase) {
        const expiryDate = new Date(
          JSON.parse(oneMonthPurchase.transactionReceipt).purchaseTime +
          30 * 24 * 60 * 60 * 1000,
        );
        console.log(
          `One month ad free expiry date: ${expiryDate} ,${expiryDate > new Date()
          }`,
        );

        return expiryDate > new Date();
      }

      return false;
    } catch (error) {
      console.error('Error checking available purchases:', error);
      return false;
    }
  }

  setupListeners() {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
    }
    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
    }

    this.purchaseUpdateSubscription = purchaseUpdatedListener(
      async purchase => {
        if (purchase) {
          console.log('Purchase updated:', purchase);

          try {
            await finishTransaction({
              purchase,
              isConsumable: true,
            });

            // Notify success through callback if provided
            if (this.onPurchaseComplete) {
              const purchaseStatus = true; // Purchase was successful
              this.onPurchaseComplete(purchase.productId, purchaseStatus);
            }
          } catch (error) {
            const timestamp = new Date().toISOString();
            const errorMessage = error.message || 'Unknown error occurred';
            const errorDetails = {
              code: error.code,
              debugMessage: error.debugMessage,
              responseCode: error.responseCode,
            };
            console.error(
              `Error finishing transaction at ${timestamp}:`,
              Object.assign({ message: errorMessage }, errorDetails),
            );
          }
        }
      },
    );

    this.purchaseErrorSubscription = purchaseErrorListener(error => {
      const currentTime = Date.now();
      // Debounce errors that occur too quickly
      if (currentTime - this.lastErrorTimestamp < this.ERROR_DEBOUNCE_MS) {
        return;
      }
      this.lastErrorTimestamp = currentTime;
      const timestamp = new Date().toISOString();
      const errorMessage = error.message || 'Unknown error occurred';
      const errorDetails = {
        code: error.code,
        debugMessage: error.debugMessage,
        responseCode: error.responseCode,
      };
      console.error(
        `Purchase error at ${timestamp}:`,
        Object.assign({ message: errorMessage }, errorDetails),
      );
    });
  }

  async getRemoveAdsProduct() {
    try {
      const products = await getSubscriptions({
        skus: [IAP_SKUS.REMOVE_ADS_Subscription],
      });
      return products[0];
    } catch (error) {
      console.error('Error getting remove ads product:', error);
      return null;
    }
  }

  async subscribeRemoveAds(product, onComplete) {
    try {
      this.onPurchaseComplete = onComplete;
      await requestSubscription({
        sku: IAP_SKUS.REMOVE_ADS_Subscription,
        ...(product.subscriptionOfferDetails && {
          subscriptionOffers: [
            {
              sku: IAP_SKUS.REMOVE_ADS_Subscription,
              offerToken: product.subscriptionOfferDetails[0].offerToken,
            },
          ],
        }),
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
      return true;
    } catch (error) {
      if (error instanceof PurchaseError) {
        if (error.code !== 'E_USER_CANCELLED') {
          Alert.alert('Purchase Error', error.message);
        }
      } else {
        Alert.alert('Error', 'Failed to complete purchase. Please try again.');
      }
      console.error('Error purchasing remove ads:', error);
      return false;
    }
  }

  async getAdFreeOneMonthProduct() {
    try {
      const products = await getProducts({
        skus: [IAP_SKUS.ONE_MONTH_AD_FREE],
      });
      return products[0];
    } catch (error) {
      console.error('Error getting one month product:', error);
      return null;
    }
  }

  async purchaseAdFreeOneMonth(onComplete) {
    try {
      this.onPurchaseComplete = onComplete;
      await requestPurchase({
        skus: [IAP_SKUS.ONE_MONTH_AD_FREE],
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
      return true;
    } catch (error) {
      if (error instanceof PurchaseError) {
        if (error.code !== 'E_USER_CANCELLED') {
          Alert.alert('Purchase Error', error.message);
        }
      } else {
        Alert.alert('Error', 'Failed to complete purchase. Please try again.');
      }
      console.error('Error purchasing one month ad free:', error);
      return false;
    }
  }

  cleanup() {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
    }
    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
    }
  }
}

export const iapService = new IAPService();
