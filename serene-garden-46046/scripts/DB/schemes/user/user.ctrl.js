/* eslint-disable max-statements */
const User = require("./user");
const Settings = require("./settings");
exports.create = function (sender) {
  let user = new User();
  user.sessionId = sender.sessionId;
  user.channelId = sender.channelId;
  user.deviceId = sender.deviceId;
  return user.save();
};
exports.find = (sender) => {
  return User.findOneAndUpdate(
    { deviceId: sender.deviceId, channelId: sender.channelId },
    sender,
    { upsert: true, new: true }
  )
    .populate({ path: "settings" })
    .exec();
};
exports.update = async (sender, settings) => {
  if (sender.settings) {
    Settings.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "user",
        },
      },

      {
        $project: {
          doesUserExist: { $gt: [{ $size: "$user" }, 0] },
        },
      },
      { $match: { doesUserExist: false } },
    ]).exec((err, result) => {
      if (err) console.log(err);
      if (result) {
        let idsList = result.map(function (d) {
          return d._id;
        });
        console.log("to be deleted ", idsList.length);
        if (idsList.length != 0)
          Settings.deleteMany({ _id: { $in: idsList } }).exec();
      }
    });
    await Settings.updateOne({ _id: sender.settings }, settings);
  } else {
    let i = new Settings({ user: sender, ...settings });
    await i.save((err, data) => {
      sender.settings = data;
      sender.save();
    });
  }
};
exports.block = (sender, blockedUser) => {
  return User.findOneAndUpdate(
    { deviceId: sender.deviceId, channelId: sender.channelId },
    { $addToSet: { blockList: blockedUser } }
  ).exec();
};
exports.report = async (sender, reportedUser) => {
  User.findOneAndUpdate(
    { deviceId: sender.deviceId, channelId: sender.channelId },
    { $addToSet: { reportList: reportedUser } }
  ).exec();
  let reportedUserObj = await User.findOne({ _id: reportedUser }).exec();
  const today = new Date(),
    yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  if (
    reportedUserObj.lastReported?.date &&
    reportedUserObj.lastReported.date > yesterday
  ) {
    reportedUserObj.lastReported.count += 1;
    reportedUserObj.lastReported.date = today;
  } else {
    reportedUserObj.lastReported = { date: new Date(), count: 1 };
  }
  reportedUserObj.save();
  return reportedUserObj;
};
exports.ban = (reportedUser) => {
  reportedUser.banCount += 1;
  reportedUser.save();
};
