import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';

import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { TooltipState } from './src/store/tooltip/atoms';
import { useRecoilValue } from 'recoil';

import Home from './src/pages/Home';
import UserAgreement from './src/pages/UserAgreement';
import Camera from './src/Components/InputSection/Camera';
import Gallary from './src/Components/InputSection/gallary';
import TermsConditions from './src/pages/termsConditions';
import Settings from './src/pages/Settings';
import { Provider as PaperProvider } from 'react-native-paper';
import theme from './src/Constants/theme';
import { SettingsProvider } from './src/contexts/SettingsContext';
import { IAPProvider, getAdFreeStatus } from './src/contexts/IAPContext';
import { RecoilRoot } from 'recoil';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { MenuProvider } from 'react-native-popup-menu';
import { MediaPage } from './src/Components/InputSection/Camera/MediaPage';
import SplashScreen from 'react-native-splash-screen';
import remoteConfig from '@react-native-firebase/remote-config';
import { initializeInterstitialAds } from './src/Components/ads/Interstitials';

import { useAppOpen } from './src/Components/hook/useAppOpen';
import { KeyboardProvider } from "react-native-keyboard-controller";
import { Stage } from './src/store/tooltip/enum';
const Stack = createNativeStackNavigator();

const App = () => {
  const toolTipStage = useRecoilValue(TooltipState);
  let [showTerms, setShowTerms] = useState();
  useAppOpen(toolTipStage === Stage.End || toolTipStage === Stage.Empty);

  // Initialize ads after a delay to allow IAP context to load
  useEffect(() => {
    const initAds = () => {
      remoteConfig()
        .ensureInitialized()
        .finally(() => {
          // Use cached ad-free status
          const isAdFree = getAdFreeStatus();
          if (!isAdFree) {
            console.log('User does not have ad-free status, initializing ads');
            initializeInterstitialAds();
          } else {
            console.log('User has ad-free status, skipping ad initialization');
          }
        });
    };

    // Delay ad initialization to allow IAP context to initialize
    const timer = setTimeout(initAds, 2000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    AsyncStorage.getItem('termsAcceptance').then(data => {
      if (data === 'true') {
        setShowTerms(false);
      } else {
        setShowTerms(true);
      }
      SplashScreen.hide();
    });
  }, []);
  if (showTerms === undefined) {
    return null;
  }

  return (
    <RecoilRoot>
      <SafeAreaProvider>
        <GestureHandlerRootView style={styles.flex}>
          <NavigationContainer>
            <IAPProvider>
              <SettingsProvider>
                <KeyboardProvider>
                  <MenuProvider>
                    <PaperProvider theme={theme}>
                      <Stack.Navigator screenOptions={{ headerShown: false }}>
                        {showTerms && (
                          <>
                            <Stack.Screen
                              name="UserAgreement"
                              component={UserAgreement}
                              navigationOptions={{
                                headerLeft: () => null,
                              }}
                            />
                            <Stack.Screen
                              name="TermsConditions"
                              component={TermsConditions}
                              navigationOptions={{
                                headerLeft: () => null,
                              }}
                            />
                          </>
                        )}
                        <Stack.Screen name="Home" component={Home} />

                        <Stack.Screen name="Settings" component={Settings} />
                        <Stack.Screen name="Camera" component={Camera} />
                        <Stack.Screen name="Gallary" component={Gallary} />
                        <Stack.Screen
                          name="MediaPage"
                          component={MediaPage}
                          options={{
                            animation: 'none',
                            presentation: 'transparentModal',
                          }}
                        />
                      </Stack.Navigator>
                    </PaperProvider>
                  </MenuProvider>
                </KeyboardProvider>
              </SettingsProvider>
            </IAPProvider>
          </NavigationContainer>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </RecoilRoot>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
});

export default App;
